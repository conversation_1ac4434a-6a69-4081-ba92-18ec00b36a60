import { Posts } from "@/components/PostsQuery";
import { sanityFetch } from "@/sanity/lib/live";
import { POSTS_QUERY } from "@/sanity/lib/queries";


export default async function Home() {
  const { data: posts } = await sanityFetch({ 
    query: POSTS_QUERY,
   })
  return (
    
      <main className="flex flex-col text-zinc-900 bg-amber-100 items-center justify-center min-h-screen">
        <Posts posts={posts} />
      </main>
    
  );
}
