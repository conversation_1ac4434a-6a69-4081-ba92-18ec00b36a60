import { SanityLive } from "@/sanity/lib/live";
import { VisualEditing } from "next-sanity";
import { draftMode } from "next/headers";
import { DisabledDraftmode } from "@/components/DisableDraftmode";

export default async function RootLayout({
     children,
}: Readonly<{
     children:React.ReactNode;
}>) {
     return (
          <div className="min-h-screen bg-white">
               { children }
               <SanityLive />
               {(await draftMode()).isEnabled && (
                    <>
                    <DisabledDraftmode />
                    <VisualEditing />
                    </>
               )}
          </div>
     );
}