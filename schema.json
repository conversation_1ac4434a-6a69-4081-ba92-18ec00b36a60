[{"name": "post", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "post"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "author": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "author"}, "optional": true}, "mainImage": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "categories": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "category", "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "publishedAt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "body": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}, "optional": true}}}, {"name": "author", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "author"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "bio": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": []}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "category", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "category"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "blockContent", "type": "type", "value": {"type": "array", "of": {"type": "union", "of": [{"type": "object", "attributes": {"children": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"marks": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "text": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "span"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "style": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "normal"}, {"type": "string", "value": "h1"}, {"type": "string", "value": "h2"}, {"type": "string", "value": "h3"}, {"type": "string", "value": "h4"}, {"type": "string", "value": "blockquote"}]}, "optional": true}, "listItem": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "bullet"}]}, "optional": true}, "markDefs": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"href": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "link"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "block"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}, {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}]}}}, {"name": "sanity.imagePaletteSwatch", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePaletteSwatch"}}, "background": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "foreground": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "population": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imagePalette", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePalette"}}, "darkMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "darkVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "vibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "dominant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "muted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}}}}, {"name": "sanity.imageDimensions", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageDimensions"}}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "aspectRatio": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageHotspot", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageHotspot"}}, "x": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "y": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageCrop", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageCrop"}}, "top": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.fileAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.fileAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "metadata": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageMetadata"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageMetadata", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageMetadata"}}, "location": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}, "dimensions": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageDimensions"}, "optional": true}, "palette": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePalette"}, "optional": true}, "lqip": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "blurHash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hasAlpha": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isOpaque": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "geopoint", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "geopoint"}}, "lat": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "lng": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "slug", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "slug"}}, "current": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assetSourceData", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assetSourceData"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}]