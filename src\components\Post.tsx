import { PortableText } from "next-sanity";
import Image from "next/image";
import { POST_QUERYResult } from "../../sanity.types";
import { urlFor } from "@/sanity/lib/image";

export function Post({ post }: { post: POST_QUERYResult }) {
  if (!post) {
    return null;
  }

  return (
    <article className="container mx-auto max-w-4xl px-4 py-8">
      {/* Title */}
      {post.title && (
        <h1 className="text-4xl font-bold text-gray-900 mb-8">{post.title}</h1>
      )}

      {/* Main Image */}
      {post.mainImage && (
        <div className="mb-8">
          <Image
            src={urlFor(post.mainImage).width(800).height(400).url()}
            alt={post.mainImage.alt || post.title || "Post image"}
            width={800}
            height={400}
            className="w-full h-auto rounded-lg shadow-lg"
          />
        </div>
      )}

      {/* Body Content */}
      {post.body && (
        <div className="prose prose-lg max-w-none">
          <PortableText value={post.body} />
        </div>
      )}
    </article>
  );
}
